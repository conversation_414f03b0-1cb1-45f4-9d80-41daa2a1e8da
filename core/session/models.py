"""
    __init__.py
"""
from dataclasses import dataclass, field
from typing import List, Any, Dict


@dataclass
class Session:
    session_id: str
    conversation: List[Dict[str, Any]]
    session_path: str
    session_name: str
    deleted: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """将Session对象转换为字典"""
        return {
            "session_id": self.session_id,
            "conversation": self.conversation,
            "session_path": self.session_path,
            "session_name": self.session_name,
            "deleted": self.deleted,
            **{k: v for k, v in self.__dict__.items() 
               if k not in ["session_id", "conversation", "session_path", "session_name", "deleted"]}
        }
    
    @classmethod
    def from_dict(cls, session_dict: Dict[str, Any]) -> 'Session':
        """从字典创建Session对象"""
        return cls(**session_dict) 