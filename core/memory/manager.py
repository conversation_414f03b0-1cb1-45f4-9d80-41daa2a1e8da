"""
    __init__.py
"""
import os
import numpy as np
import openai
from typing import List, Optional, Dict, Any
from .models import Memory, Label, LabelManager
from .doc_storage import DocMemoryHandler


class MemoryManager:
    """
        数据管理层，增删改查，治理
    """
    def __init__(self, root_dir: str = "./data/memory4doc"):
        """
        初始化记忆管理器
        """
        self.dimension = 1536                       # OpenAI ada-002 embedding dimension
        self.next_memory_id = 1                     # 记忆ID计数器
        self.next_label_id = 1                      # 标签ID计数器
        self._root_memory_dir = root_dir            # 存储路径
        self._memory_data: List[Memory] = []        # 存储记忆
        self._labels: Dict[str, Label] = {}         # 存储标签
        self._incompleted_memory_ids = set()
        self.handler = DocMemoryHandler()   # 文档持久化处理
        # self.index = faiss.IndexFlatL2(self.dimension)

    def load_memory(self, root_dir: str = "./data/memory4doc"):
        """加载记忆
        
        Args:
            root_dir: 根目录路径
        """
        self._root_memory_dir = root_dir
        memories, labels = self.handler.load(root_dir)
        self._memory_data.clear()
        self._labels.clear()
        for mem in memories:
            if 'description' in mem.labels or 'labels' in mem.labels:
                continue
            self._memory_data.append(mem)

        for lab in labels:
            self._labels[lab.label] = lab


    def save_memory(self):
        """保存记忆
        """
        memories = self._memory_data
        lm = LabelManager(self._labels, len(self._labels))
        print(self._root_memory_dir + "description.md")
        memories.append(
            Memory(
                id=-1,
                labels=['labels'],
                summary='labels',
                uri=self._root_memory_dir + "/description.md",
                original_text=lm.to_markdown()
                )
        )
                               
        self.handler.save(memories) # save
 
    def _get_embedding(self, text: str) -> np.ndarray:
        """获取文本的向量嵌入"""
        client = openai.OpenAI(
            base_url=os.getenv("EMBEDDING_BASE_URL"),
            api_key=os.getenv("EMBEDDING_API_KEY")
        )
        response = client.embeddings.create(
            input=text,
            model="text-embedding-ada-002"
        )
        return np.array(response.data[0].embedding, dtype=np.float32)
 
    def search_memory(self, query: str, labels: List[str] = None, k: int = 5,
                      search_type: str = "keyword") -> List[Memory]:
        """
            Search memories using different methods
        """
        
        if not query:
            return self._memory_data
        
        if search_type == "keyword":
            matches = [mem for mem in self._memory_data
                      if query.lower() in mem.original_text.lower() or 
                         query.lower() in mem.summary.lower()]
            return matches[::-1][:k]
        
        elif search_type == "label":
            if labels is None or not labels:
                return []
            
            # 举例：labels 是 A、B、C，mem 是 A、B、C、D，则 mem 可召回
            matches = [mem for mem in self._memory_data
                      if all(lid in mem.labels for lid in labels)]
            return matches[::-1][:k]
        
        else:
            raise ValueError(f"Invalid search type: {search_type}")
 
    def add_memory(self, memory_text: str, summary: str, labels: List[str] = None, metadata: str = ""
                   , save_path: str = None):
        """添加新的记忆"""
        # 确保label_ids不为空
        if not labels:
            raise ValueError("标签不能为空，请至少选择一个标签")
        
        embedding = None  # self._get_embedding(summary)

        uri = str(
            save_path + "/" + os.path.basename(save_path) + ".md"
            + "?label=" 
            + "&label=".join(labels)
            + "&id=" +  str(self.next_memory_id)
        )

        memory = Memory(
            id=self.next_memory_id,
            original_text=memory_text,
            summary=summary,
            labels=labels,
            embedding=embedding,
            metadata=metadata,
            uri=uri
        )

        self._memory_data.append(memory)
        self.next_memory_id += 1

    def update_memory(self, memory_id: int, new_text: str = None, new_summary: str = None,
                      new_labels: List[str] = None, new_metadata: str = None, save_path: str = None):
        """Update an existing memory
        
        Args:
            memory_id: ID of memory to update
            new_text: New text content
            new_summary: New summary
            new_label_ids: New label_ids
            new_metadata: New metadata
        """
        if not new_labels:
            raise ValueError("标签不能为空，请至少选择一个标签")

        for mem in self._memory_data:
            if mem.id == memory_id:
                # 确保label_ids不重复
                if new_labels:
                    new_labels = list(set(new_labels))
                uri = str(
                    save_path + "/" + os.path.basename(save_path) + ".md"
                    + "?label=" 
                    + "&label=".join(new_labels)
                    + "&id=" +  str(mem.id)
                )
                mem.update(new_text, new_summary, new_labels, new_metadata, uri)                
                return
 
        raise ValueError(f"Memory with id {memory_id} not found")
 
    def delete_memory(self, memory_id: str):
        """Delete a memory by ID
        
        Args:
            memory_id: ID of memory to delete
        """
        for i, mem in enumerate(self._memory_data):
            if mem.id == memory_id:       
                del self._memory_data[i]
                return
 
        raise ValueError(f"Memory with id {memory_id} not found")
 
    def add_label(self, label: str, description: str, category: str = "default", 
                  synonyms: str = "", parent_id: Optional[int] = None):
        """添加新label
        
        Args:
            label: 标签名称
            description: 标签描述
            category: 标签类别
            synonyms: 同义词用逗号分隔
            parent_id: 父标签ID
        """
        # Ensure category is a string and parent_id is either None or int
        category = str(category) if category is not None else "default"
        parent_id = int(parent_id) if parent_id is not None else None
        
        # 检查标签是否已存在
        for _label in self._labels.values():
            if label == _label.label:
                return 
            
        # 创建新标签
        new_label = Label(
            id=self.next_label_id,
            label=label,
            description=description,
            category=category,
            synonyms=synonyms,
            parent_id=parent_id,
            children_ids=[]
        )
        
        # 更新标签字典和ID计数器
        self._labels[label] = new_label
        self.next_label_id += 1
        
        # 处理父子关系
        if parent_id is not None:
            self.add_child_to_parent(parent_id, new_label.id)

    def update_label(self, old_label: str, new_label: str, new_description: str = None,
                     new_category: str = None, new_synonyms: str = "",
                     new_parent_id: Optional[int] = None):
        """Update a label and its description
        
        Args:
            old_label: 原标签名称
            new_label: 新标签名称
            new_description: 新描述
            new_category: 新类别
            new_synonyms: 新同义词列表
            new_parent_id: 新父标签ID
        """
        label_id = None
        label_obj = None
        for lab in self._labels.values():
            if lab.label == old_label:
                label_id = lab.id
                label_obj = lab
                break
        
        if label_id is None:
            raise ValueError(f"Label '{old_label}' not found")
        
        label_obj.update(new_label, new_description, new_category, new_synonyms)
            
        # 处理父子关系变更
        if new_parent_id is not None and new_parent_id != label_obj.parent_id:
            # 从旧父标签中移除
            if label_obj.parent_id is not None:
                for parent_label in self._labels.values():
                    if parent_label.id == label_obj.parent_id and label_id in parent_label.children_ids:
                        parent_label.children_ids.remove(label_id)
                        
            # 添加到新父标签
            label_obj.parent_id = new_parent_id
            self.add_child_to_parent(new_parent_id, label_id)

    def delete_label(self, label: str) -> Dict[str, str]:
        """Delete a label and remove it from all memories"""
         # 从所有记忆中检查这个标签ID
        for memory in self._memory_data:
            if label in memory.labels:
                return {"success": False, 
                        "message": f"Label {label} found in memory ID {memory.id}. Aborting removal."}
        
        # 获取要删除的标签的ID
        label_id = None
        for i ,lab in enumerate(self._labels.values()):
            if lab.label == label:
                label_id = lab.id

                 # 从父标签的children_ids中移除
                if lab.parent_id is not None:
                    for parent_label in self._labels.values():
                        if parent_label.id == lab.parent_id and lab.id in parent_label.children_ids:
                            parent_label.children_ids.remove(lab.id)

                # 将子标签的parent_id设为None
                for child_id in lab.children_ids:
                    for child_label in self._labels.values():
                        if child_label.id == child_id:
                            child_label.parent_id = None

                # 删除标签
                del self._labels[label]
                
                break
        
        if label_id is None:
            raise ValueError(f"Label '{label}' not found")
        
        return {"success": True, "message": "Label deleted successfully!"}

    def list_labels(self) -> List[Dict[str, Any]]:
        """列举标签（标签名、描述、关联关系、同义词、Label的领域）"""
        return [
            {
                "id": label.id,
                "name": label.label,
                "description": label.description,
                "category": label.category,
                "synonyms": label.synonyms,
                "parent_id": label.parent_id,
                "children_ids": label.children_ids
            }
            for label in self._labels.values()
        ]

    def add_child_to_parent(self, parent_id: int, child_id: int) -> bool:
        """添加子标签到父标签
        
        Args:
            parent_id: 父标签ID
            child_id: 子标签ID
            
        Returns:
            bool: 是否成功添加
        """
        # 查找父标签
        parent_label = None
        for label in self._labels.values():
            if label.id == parent_id:
                parent_label = label
                break
                
        if parent_label is None:
            return False
            
        # 添加子标签ID
        if child_id not in parent_label.children_ids:
            parent_label.children_ids.append(child_id)
        
        return True

if __name__ == "__main__":
    # 示例使用
    manager = MemoryManager()
    output_dir = "data/memory4doc"
    
    # 导出标签信息为单个 memory 文件
    print("\nExporting labels as memory...")
    try:
        manager.export_labels_as_memory(output_dir)
        print("Successfully exported labels")
    except Exception as e:
        print(f"Failed to export labels: {str(e)}")
    
    # 导出所有记忆
    print("\nExporting memories...")
    for memory in manager.memory_data:
        try:
            manager.export_memory_to_markdown_file(memory, output_dir)
            print(f"Successfully exported memory {memory.id}")
        except Exception as e:
            print(f"Failed to export memory {memory.id}: {str(e)}")
