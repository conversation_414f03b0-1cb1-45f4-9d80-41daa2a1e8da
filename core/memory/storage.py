"""
    __init__.py
"""
import sqlite3
import json
from datetime import datetime
from typing import Dict, Set, List, Optional
from .models import Memory, Label
import os


class MemoryHandler:
    def __init__(self):
        self.conn = self.connect_db()
        self.create_table()
 
    @staticmethod
    def connect_db():
        """连接到数据库，支持自定义数据库路径"""
        db_path = os.environ.get('MEMORY_DB_PATH', "./data/memory/memory-2.db")
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        return sqlite3.connect(db_path)
 
    def create_table(self):
        """Create necessary database tables"""
        create_memory_table_sql = """
            CREATE TABLE IF NOT EXISTS Memory (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                original_text TEXT NOT NULL,
                summary TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                label_ids TEXT,  -- Stored as comma-separated integers
                embedding BLOB,
                metadata TEXT
            );
        """
        
        create_label_table_sql = """
            CREATE TABLE IF NOT EXISTS Labels (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                label TEXT NOT NULL,
                description TEXT,
                category TEXT NOT NULL,
                parent_id INTEGER,
                synonyms TEXT,  -- 将 synonyms 存储为 JSON 字符串
                FOREIGN KEY (parent_id) REFERENCES Labels (id)
            );
        """
        
        self.conn.execute(create_memory_table_sql)
        self.conn.execute(create_label_table_sql)
        self.conn.commit()

    def insert_memory(self, memory):
        insert_sql = """
            INSERT INTO Memory (original_text, summary, created_at, updated_at, label_ids, embedding, metadata)
            VALUES (?, ?, ?, ?, ?, ?, ?);
        """
        self.conn.execute(insert_sql, (
            memory.original_text,
            memory.summary,
            memory.created_at,
            memory.updated_at,
            ','.join(map(str, memory.label_ids)) if memory.label_ids else '',
            memory.embedding,
            memory.metadata
        ))
        self.conn.commit()

    def query_memories(self):
        cursor = self.conn.execute("SELECT * FROM Memory;")
        columns = [description[0] for description in cursor.description]
        memories = []
        
        for row in cursor:
            # Create a dictionary of column names to values
            row_dict = dict(zip(columns, row))
            
            memory = Memory(
                id=row_dict['id'],
                original_text=row_dict['original_text'],
                summary=row_dict['summary'],
                created_at=row_dict['created_at'],
                updated_at=row_dict['updated_at'],
                label_ids=list(map(int, row_dict['label_ids'].split(','))) if row_dict['label_ids'] else [],
                embedding=row_dict['embedding'],
                metadata=row_dict['metadata']
            )
            memories.append(memory)
        
        return memories
 
    def upd_memory(self, memory_id, mem):
        update_sql = """
            UPDATE Memory
            SET original_text = ?, summary = ?, label_ids = ?, updated_at = ?, metadata = ?
            WHERE id = ?;
        """
        self.conn.execute(update_sql,
                         (mem.original_text, mem.summary, 
                          ','.join(map(str, mem.label_ids)), 
                          datetime.now(), mem.metadata, memory_id))
        self.conn.commit()
 
    def del_memory(self, memory_id):
        delete_sql = "DELETE FROM Memory WHERE id = ?;"
        self.conn.execute(delete_sql, (memory_id,))
        self.conn.commit()

    def insert_label(self, label: str, description: str, category: str, synonyms: str, parent_id: int):
        """插入新标签
        
        Args:
            label: 标签名称
            description: 标签描述
            category: 标签类别
            synonyms: 同义词逗号分隔
            parent_id: 父标签ID
        """
        
        cursor = self.conn.execute(
            "INSERT INTO Labels (label, description, category, synonyms, parent_id) VALUES (?, ?, ?, ?, ?)",
            (label, description, category, synonyms, parent_id)
        )
        label_id = cursor.lastrowid
        self.conn.commit()
        return label_id

    def query_labels(self) -> Dict[int, Label]:
        """查询所有标签"""
        labels = {}
        cursor = self.conn.execute("SELECT id, label, description, category, synonyms, parent_id FROM Labels")
        for row in cursor:
            label_id, label, description, category, synonyms, parent_id = row
            
            labels[label_id] = Label(
                id=label_id,
                label=label,
                description=description,
                category=category,
                synonyms=synonyms,
                parent_id=parent_id
            )
        return labels
 
    def upd_label(self, old_label_id: int, new_label: str, new_description: str,
                  new_category: str, new_synonyms: str, new_parent_id: int):
        """Update a label in the database"""
        update_sql = """
            UPDATE Labels
            SET label = ?, description = ?, category = ?, synonyms = ?, parent_id = ?
            WHERE id = ?;
            """
        self.conn.execute(update_sql, (new_label, new_description, new_category,
                          new_synonyms, new_parent_id, old_label_id))
        self.conn.commit()

    def del_label(self, label: str):
        """Delete a label from the database"""
        delete_sql = "DELETE FROM Labels WHERE label = ?;"
        self.conn.execute(delete_sql, (label,))
        self.conn.commit()

    def clean_duplicates(self):
        """清理数据库中的重复记录，保留ID较小的记录"""
        # 找出所有重复的记录
        find_duplicates_sql = """
            WITH DuplicateGroups AS (
                SELECT 
                    MIN(id) as min_id,
                    summary,
                    COUNT(*) as count
                FROM Memory 
                GROUP BY summary
                HAVING COUNT(*) > 1
            )
            SELECT m.id, m.summary
            FROM Memory m
            INNER JOIN DuplicateGroups d ON m.summary = d.summary
            WHERE m.id > d.min_id;
        """
        
        cursor = self.conn.execute(find_duplicates_sql)
        duplicate_records = cursor.fetchall()
        
        if not duplicate_records:
            print("没有发现重复记录")
            return
        
        # 删除重复记录
        for record in duplicate_records:
            delete_sql = "DELETE FROM Memory WHERE id = ?;"
            self.conn.execute(delete_sql, (record[0],))
            print(f"删除重复记录 ID: {record[0]}, Summary: {record[1][:50]}...")
        
        self.conn.commit()
        print(f"共清理了 {len(duplicate_records)} 条重复记录")

    def add_child_to_parent(self, parent_id: int, child_id: int):
        """Add a child label to a parent label"""
        update_sql = """
            UPDATE Labels
            SET parent_id = ?
            WHERE id = ?;
        """
        # TODO: add child_id to parent_id
        self.conn.execute(update_sql, (parent_id, child_id))
        self.conn.commit()
