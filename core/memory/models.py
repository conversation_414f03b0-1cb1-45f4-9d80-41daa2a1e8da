"""
    __init__.py
"""
import os
from dataclasses import dataclass, field
from datetime import datetime
from typing import List, Optional, Any, Dict, Tuple
import numpy as np
import json
import re


@dataclass
class Memory:
    """ 记忆片段 """
    id: int
    original_text: str
    summary: str
    uri: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    labels: List[str] = field(default_factory=list)
    embedding: Optional[np.ndarray] = None
    metadata: Optional[dict] = field(default_factory=dict)

    def update(self, new_text: Optional[str] = None,
               new_summary: Optional[str] = None, labels: Optional[List[str]] = None,
               metadata: Optional[dict] = None, uri: Optional[str] = None):
        """"""
        if new_text is not None:
            self.original_text = new_text
        if new_summary is not None:
            self.summary = new_summary
        if labels is not None:
            self.labels = labels
        if metadata is not None:
            self.metadata = metadata
        if uri is not None:
            self.uri = uri
        self.updated_at = datetime.now()
    
    @classmethod
    def from_markdown(cls: 'Memory', markdown: str) -> 'Memory':
        """从Markdown格式的字符串中解析记忆片段"""
        # 使用正则表达式提取数据
        id_match = re.search(r"<id>(.*?)</id>", markdown)
        created_at_match = re.search(r"<created_at>(.*?)</created_at>", markdown)
        updated_at_match = re.search(r"<updated_at>(.*?)</updated_at>", markdown)
        labels_match = re.search(r"<labels>(.*?)</labels>", markdown)
        summary_match = re.search(r"<summary>(.*?)</summary>", markdown)
        original_text_match = re.search(r"<original_text>(.*?)</original_text>", markdown, re.DOTALL)
        metadata_match = re.search(r"<metadata>\n```json\n(.*?)\n```\n</metadata>", markdown, re.DOTALL)

        # 解析提取到的数据
        return cls(
            id = int(id_match.group(1)) if id_match else -1,
            created_at = created_at_match.group(1) if created_at_match else datetime.now,
            updated_at = updated_at_match.group(1) if updated_at_match else datetime.now,
            labels = labels_match.group(1).split(', ') if labels_match else [],
            summary = summary_match.group(1) if summary_match else '',
            original_text = original_text_match.group(1).strip() if original_text_match else markdown,
            metadata = json.loads(metadata_match.group(1)) if metadata_match else {}
        )

    def to_markdown(self, ignore_metadata: bool = False) -> str:
        """将记忆片段转换为Markdown格式"""
        md = []
        # 添加元数据
        md.append(f"<id>{self.id}</id>\n")
        md.append(f"<created_at>{self.created_at}</created_at>\n")
        md.append(f"<updated_at>{self.updated_at}</updated_at>\n")

        # 添加标签
        md.append(f"<labels>{', '.join(self.labels)}</labels>\n")
    
        # 添加摘要
        md.append(f"<summary>{self.summary}</summary>\n")
        
        # 添加原始文本
        md.append("<original_text>\n")
        md.append(self.original_text)
        md.append("\n</original_text>\n")
        
        if self.metadata and not ignore_metadata:
            md.append("<metadata>\n```json\n" + \
                        json.dumps(self.metadata, ensure_ascii=False, indent=4) + \
                        "\n```\n</metadata>")
        
        return "\n".join(md)
    

@dataclass
class Label:
    """"""
    id: int
    label: str
    description: str
    category: str = "default"
    synonyms: List[str] = field(default_factory=list)
    parent_id: Optional[int] = None
    children_ids: List[int] = field(default_factory=list)

    def update(self, new_label: Optional[str] = None,
               description: Optional[str] = None, category: Optional[str] = None,
               synonyms: List[str] = field(default_factory=list)):
        """"""
        if new_label is not None:
            self.label = new_label
        if description is not None:
            self.description = description
        if category is not None:
            self.category = category
        if synonyms is not None:
            self.synonyms = synonyms


@dataclass
class LabelManager:
    """
        标签管理器
    """
    labels: Dict[str, Label] = field(default_factory=dict) # Key 为 label 名称
    num: int = 0

    def extend(self, labels: List[Label]) -> None:
        """ 批量添加标签
        """
        for label in labels:
            if label.label in self.labels:
                continue
            self.labels[label.label] = label
        self.num += len(labels)

    def to_markdown(self) -> str:
        """ 将标签管理器转换为Markdown 表格格式
        """
        # 构建表格头部
        table_header = "| ID | 标签 | 描述 | 类别 | 父标签ID | 同义词 |"
        table_separator = "|-----|------|------|------|----------|--------|"
        # 构建表格内容
        table_content = ""
        for label in self.labels.values():
            # 获取父标签名称
            parent_name = ""
            if label.parent_id is not None:
                for parent_label in self.labels.values():
                    if parent_label.id == label.parent_id:
                        parent_name = parent_label.label
                        break
            # 构建表格行
            row = f"| {label.id} | {label.label} | {label.description} | {label.category} "\
                  f"| {parent_name} | {label.synonyms or ''} |\n"
            table_content += row
        return "\n".join([table_header, table_separator, table_content])
    

    @classmethod
    def from_markdown(cls: 'LabelManager', markdown: str) -> 'LabelManager':
        """ 从Markdown 表格格式中解析标签管理器
        """
        cls = LabelManager()
        # 按行解析表格内容
        lines = markdown.strip().split('\n')
        if len(lines) < 5:  # 至少需要表头、分隔符和一行数据
            return cls
        
        # 跳过表头和分隔符
        for line in lines[4:]:
            if not line.strip():
                continue
        
            # 解析表格行
            parts = [part.strip() for part in line.strip('|').split('|')]
            if len(parts) < 4:  # 至少需要ID、标签、描述和类别
                continue

            try:
                # 提取标签属性
                label_id = int(parts[0])
                label_name = parts[1]
                description = parts[2]
                category = parts[3]
                parent_name = parts[4]
                # 提取可选的同义词
                synonyms = parts[5] if len(parts) > 5 else ""
                
                # 创建Label对象
                label = Label(
                    id=label_id,
                    label=label_name,
                    description=description,
                    category=category,
                    parent_id=parent_name,
                    children_ids=[],  # 子标签关系后续处理
                    synonyms=synonyms
                )
                
                # 更新labels字典和next_label_id
                cls.labels[label_name] = label
                cls.num = cls.num + 1
            except (ValueError, AttributeError, TypeError) as e:
                print(f"Warning: Failed to parse label definition: {line}. Error: {str(e)}")

        # 提取可选的父标签ID
        parent_name = None
        # 处理父子标签关系
        for label in cls.labels.values():
            if label.parent_id:
                parent_name = label.parent_id
                for parent_label in cls.labels.values():
                    if parent_label.label == parent_name:
                        label.parent_id = parent_label.id
                        if label.id not in parent_label.children_ids:
                            parent_label.children_ids.append(label.id)
        return cls
                        
