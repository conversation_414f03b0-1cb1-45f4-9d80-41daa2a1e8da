"""
    __init__.py
"""
import os
import re
import hashlib
import numpy as np
from datetime import datetime
from typing import List, Dict, Optional, Set, Tuple
from pathlib import Path
from .models import Memory, Label, LabelManager


class DocMemoryHandler:
    """
        记忆存储的加载和保存处理器
    """
    
    def __init__(self):
        """
        初始化处理器
        """
        self.memories_md5_dict: Dict[str, str] = {}
    
    def load(self, root_dir: str) -> Tuple[List[Memory], List[Label]]:
        """加载整个记忆目录"""
        self._root_dir = root_dir
        self.memories_md5_dict: Dict[str, str] = {}

        # 递归扫描所有目录和文件
        memories, labels = self._load(self._root_dir)

        for memory in memories:
            md5 = memory.metadata.get("md5")
            if md5 is None:
                md5 = hashlib.md5(memory.to_markdown(ignore_metadata=True).encode()).hexdigest()
                memory.metadata["md5"] = md5
            self.memories_md5_dict[md5] = memory.to_markdown(ignore_metadata=True).encode()

        return memories, labels

    def _load(self, directory: str) -> Tuple[List[Memory], List[Label]]:
        """加载目录或文件"""        
        memories = []
        label_manager = LabelManager()

        # 确保目录存在
        if not os.path.exists(directory):
            return memories, label_manager.labels.values()

        if os.path.isdir(directory):
            # 是目录，处理目录下的所有文件和子目录
            for item in os.listdir(directory):
                item_path = os.path.join(directory, item)
                mem, lab = self._load(item_path)
                memories.extend(mem)
                label_manager.extend(lab)

        elif os.path.isfile(directory) and directory.endswith('.md'):
            # 是.md文件，处理它
            mem, lab = self._process_markdown_file(directory)
            memories.extend(mem)
            label_manager.extend(lab)

        return memories, label_manager.labels.values()

    def _process_markdown_file(self, file_path: str) -> Tuple[List[Memory], List[Label]]:
        """处理单个Markdown文件"""
        memories = []
        label_manager = LabelManager()

        # 读取文件解析 Memory Blocks
        content = self._read_file(file_path)
        memories = self._parse_markdown_content(content)
        
        # 先尝试获取所有该文件下的 Labels
        for memory in memories:
            if 'label' in memory.labels:
                label_manager = LabelManager.from_markdown(memory.original_text)
          

        # 生成文件路径相关的labels
        rel_path = os.path.relpath(file_path, self._root_dir)
        path_labels = self._generate_path_labels(rel_path)
        
        # 为每个记忆添加路径标签 & 路径
        for memory in memories:
            # 添加路径标签
            path_label_ids = path_labels
            memory.labels.extend(path_labels)
            memory.labels = list(set(memory.labels))

            # 查找下，有没有 LabelManager 没有的 Label
            for label in memory.labels:
                if label not in label_manager.labels:
                    label_manager.labels[label] = Label(id=-1, label=label, description="")
            
            memory.uri = (
                file_path 
                + "?" + "&".join([f"label={label}" for label in memory.labels]) 
                + "&id=" + str(memory.id)
            )
            
        return memories, label_manager.labels.values()

    def _generate_path_labels(self, path: str) -> List[str]:
        """
            从文件路径生成标签列表
        """
        parts = []
        for p in Path(path).parts:
            if p == ".":
                continue
            if p.endswith('.md'):
                parts.append(p.split('.')[0])
            else:
                parts.append(p)

        return parts
    
    def _read_file(self, path: str) -> str:
        """读取文件内容"""
        with open(path, 'r', encoding='utf-8') as f:
            return f.read()
 
    def _parse_markdown_content(self, content: str) -> List[Memory]:
        """
            解析Markdown内容，提取记忆片段
        """
        memories = []

        # 按'---\n---\n'分割内容
        blocks = content.split('---\n---\n')
        for block in blocks:
            if not block.strip():
                continue
            memory = Memory.from_markdown(block)
            memories.append(memory)
        
        return memories
        
    def save(self, memories: List[Memory]):
        """
            保存所有记忆到文档，只保存修改部分
        """
        uris = [] # 相同 URI 的文件都要重新写，文件内 按 ID 顺序排列
        memories.sort(key=lambda x: x.id)
        for memory in memories:
            if memory.metadata and memory.metadata["md5"] in self.memories_md5_dict:
                # 存在就跳过，不比对细节了，默认不太会出现 HASH 碰撞
                continue
            
            uris.append(memory.uri.split('?')[0])
        
        for uri in set(uris):
            self._save_memory_block(uri, memories)
    
    def _save_memory_block(self, uri: str, memories: List[Memory]):
        """
            保存单个文档的记忆
        """
        memories = [m for m in memories if m.uri.split('?')[0] == uri]
        path = uri.split('?')[0]
        # 检查目录是否存在
        if not os.path.exists(os.path.dirname(path)):
            # 如果目录不存在，则创建目录
            os.makedirs(os.path.dirname(path))
        with open(path, 'w', encoding='utf-8') as f:
            for memory in memories:
                f.write(memory.to_markdown())
                f.write("---\n---\n")
