"""
    __init__.py
"""
from http.server import HTTPServer, BaseHTTPRequestHandler
from logging import debug, info, warning, error
import logging
import optparse
from urllib.parse import parse_qs
import hashlib
import sys
from Crypto.Cipher import AES # pycrypto
import base64
import six
import json
import requests
import pandas as pd

from llm_providers import erine

global Token
global AESKey
global Webhook
global ImageResolution
global APIKeyList
global chatcontext
global userconfig

VERSION = "3.4"
access_token = "24.933476c810cb95ce05f48751aebead47.2592000.**********.282335-26619551"

TASK_CSV = "./data/task_info.csv"
task_df = pd.read_csv(TASK_CSV)


def record_task(robot):
    """
    记录任务信息
    """
    if robot.commandname is None:
        return
    global task_df
    task_df = pd.read_csv(TASK_CSV)
    new_row = pd.DataFrame({
            'time': [robot.time],
            'groupid': [robot.groupid],
            'userid': [robot.userid],
            'command': [robot.commandname],
            'message': [robot.message.replace('\n', '\\n')],
            })
    task_df = pd.concat([new_row, task_df]).reset_index(drop=True)
    task_df.to_csv(TASK_CSV, index=False)


# reference: https://qy.baidu.com/doc/index.html#/inner_serverapi/appendix
def base64_urlsafe_decode(s):
    """
    base64 解码(urlsafe兼容模式)
    :return:
    """
    # 系统的urlsafe_b64decode方法不支持补'='
    s = s.replace('-', '+').replace('_', '/') + '=' * (len(s) % 4)

    return base64.b64decode(s)


# reference: https://qy.baidu.com/doc/index.html#/inner_serverapi/appendix
class AESCipher(object):
    """
    AES加解密类
    """

    def __init__(self, key, mode=AES.MODE_ECB, padding='PKCS7', encode='base64', **kwargs):
        """
        初始化
        :param key:
        :param mode:
        :param padding: 数据填充方式 PKCS7、ZERO
        :param encode: 数据编码方式 raw、base64、hex
        """
        self.key = key
        self.mode = mode
        self.padding = padding
        self.encode = encode
        self.kwargs = kwargs

        self.bs = AES.block_size

        self.IV = self.kwargs.get('IV', None)
        if self.IV and self.mode in (AES.MODE_ECB, AES.MODE_CTR):
            raise TypeError("ECB and CTR mode does not use IV")

    def _aes(self):
        """
        AES对象
        """
        return AES.new(self.key, self.mode, **self.kwargs)

    def encrypt(self, plaintext):
        """
        加密
        :param plaintext:
        :return: py3返回 byte string, py2返回str
        """
        # padding https://en.wikipedia.org/wiki/Padding_(cryptography)#PKCS#5_and_PKCS#7
        if self.padding == 'PKCS7':
            pad = lambda s: s + (self.bs - len(s) % self.bs) \
                            * chr(self.bs - len(s) % self.bs).encode('utf-8')
        else:
            pad = lambda s: s + (self.bs - len(s) % self.bs) \
                            * '\x00'
        # 统一为字节类型
        if isinstance(plaintext, six.text_type):
            plaintext = plaintext.encode('utf-8')

        # 注意：加密、解密需单独实例化
        raw = self._aes().encrypt(pad(plaintext))

        if self.encode == 'hex':
            return binascii.hexlify(raw)
        if self.encode == 'base64':
            return base64.b64encode(raw)
        return raw

    def decrypt(self, ciphertext):
        """
        解密
        :param ciphertext:
        :return: py3返回 byte string, py2返回str
        """
        if not ciphertext:
            return None

        if self.padding == 'PKCS7':
            if six.PY3:
                unpad = lambda s: s[0:-s[-1]]
            else:
                unpad = lambda s: s[0:-ord(s[-1])]
        else:
            unpad = lambda s: s.rstrip('\x00')

        # 统一为文本字符类型
        if isinstance(ciphertext, six.binary_type) and self.encode != 'raw':
            ciphertext = ciphertext.decode('utf-8')
        if self.encode == 'hex':
            ciphertext = binascii.unhexlify(ciphertext)
        if self.encode == 'base64':
            ciphertext = base64_urlsafe_decode(ciphertext)

        return unpad(self._aes().decrypt(ciphertext))


class LLMAPI(object):
    """
    LLM API
    """
    @classmethod
    def chat_completions(self, query):
        """
        query: [{"role": "user", "content": "Hello!"}]
        """
        url = erine.LLM['ERNIE_Bot_40'] + "?access_token=" + access_token
        r = requests.request('post', url, timeout=120, json={"messages": query})
        http_status = r.status_code
        content = r.content
        if http_status == 200:
            response = r.json()
            answer = response.get("result")
        else:
            answer = content
        info("A: '%s'", answer)
        return ("assistant", answer.strip())

    @classmethod
    def text2image(self, query):
        """
        query:
        """
        return None


class UserConfig:
    """"""
    def __init__(self, file_path):
        self.file_path = file_path
        self.load_data()

    def load_data(self):
        """"""
        try:
            with open(self.file_path, "r") as f:
                self.data = json.load(f)
        except FileNotFoundError:
            self.data = {}
            self.save_data()

    def save_data(self):
        """"""
        with open(self.file_path, "w") as f:
            json.dump(self.data, f)

    def update(self, groupid, userid, systemConfig):
        """"""
        key = "{}_{}".format(groupid, userid)
        if key in self.data:
            self.data[key]["systemConfig"] = systemConfig
        else:
            self.data[key] = {"systemConfig": systemConfig}
        self.save_data()

    def delete(self, groupid, userid):
        """"""
        key = "{}_{}".format(groupid, userid)
        if key in self.data:
            del self.data[key]
            self.save_data()

    def query(self, groupid, userid):
        """"""
        key = "{}_{}".format(groupid, userid)
        if key in self.data:
            return self.data[key]["systemConfig"]
        return None


class ChatContext(object):
    """
    {
        (groupid, userid): [{
            "Q": query,
            "R": role,
            "A": answer,
            "T": timestamp
        }, {
            ......
        }]
    }
    """
    def __init__(self):
        """"""
        self.context = {}

    def add_one_Q_A(self, groupid, userid, query, role, answer, timestamp):
        """"""
        if (groupid, userid) not in self.context:
            self.context[(groupid, userid)] = []
        self.context[(groupid, userid)].append({
            "Q": query,
            "R": role,
            "A": answer,
            "T": timestamp
        })
        self.expired_delete(groupid, userid, timestamp)
        debug("context array: %s", self.context[(groupid, userid)])

    def construct_query(self, groupid, userid, query, time):
        """"""
        content = []
        user_system = userconfig.query(groupid, userid)
        if user_system is not None:
            content.append({
                "role": "system",
                "content": user_system
            })
        if (groupid, userid) not in self.context:
            content.append({
                "role": "user",
                "content": query
            })
            return content

        self.expired_delete(groupid, userid, time)
        for Q_A in self.context[(groupid, userid)]:
            content.append({
                "role": "user",
                "content": Q_A["Q"]
            })
            content.append({
                "role": Q_A["R"],
                "content": Q_A["A"]
            })
        content.append({"role": "user", "content": query})
        debug("content:\n'%s'", content)
        return content

    def expired_delete(self, groupid, userid, timestamp):
        """"""
        # len(context) > 5
        if len(self.context[(groupid, userid)]) > 5:
            self.context[(groupid, userid)].pop(0)
        # 600000ms == 600s == 10mins
        while len(self.context[(groupid, userid)]) > 0 and \
              timestamp - self.context[(groupid, userid)][0]["T"] > 600000:
            self.context[(groupid, userid)].pop(0)

    def clear(self, groupid, userid):
        """"""
        del self.context[(groupid, userid)]


class InfoflowRobot(object):
    """"""
    def message_receive(self, data):
        """"""
        json_data = json.loads(data)
        debug("response: '%s'", json_data)
        self.json_header = json_data['message']['header']
        self.json_body = json_data['message']['body']
        self.groupid = json_data['groupid']
        debug("groupid: '%d'", self.groupid)
        self.userid = self.json_header['fromuserid']
        debug("userid: '%s'", self.userid)
        self.time = json_data['time']
        debug("time: '%s'", self.time)

        message = ""
        self.commandname = None
        self.body_json = None
        self.message = ""
        for meta in self.json_body:
            if meta["type"] == "TEXT":
                message = message + meta["content"]
            if meta["type"] == "LINK":
                message = message + meta["label"]
            if meta["type"] == "AT" and 'robotid' not in meta:
                message = message + meta["name"]
            if meta["type"] == "IMAGE":
                self.add_one_message("AT")
                self.add_one_message("TEXT", " 暂不支持图像处理~ ")
                self.message_response()
                return ""
            if meta["type"] == "command":
                self.commandname = meta["commandname"]
        if self.commandname is not None:
            debug("command name: '%s'", self.commandname)
        self.message = message.strip()
        return self.message

    def add_one_message(self, msgtype, content=None):
        """"""
        if self.body_json is None:
            self.body_json = []
        if msgtype == "AT":
            self.body_json.append({
                "type": msgtype,
                "atall": False,
                "atuserids": [self.userid]
            })
        else:
            self.body_json.append({
                "type": msgtype,
                "content": content
            })

    def message_response(self):
        """"""
        header = {"Content-Type": "application/json"}
        response_json = {
            "message": {
                "header": {
                    "toid": self.groupid,
                    "totype": "GROUP",
                    "msgtype": "TEXT"
                },
                "body": self.body_json
            }
        }
        data = json.dumps(response_json)

        response = requests.post(Webhook, headers=header, data=data)
        debug("response: '%s'", response)
        self.body_json = None

    def query_chatgpt_api(self):
        """"""
        try:
            if self.commandname == "image":
                if len(self.message) == 0:
                    return
                image_data = LLMAPI.text2image(self.message)
                self.add_one_message("IMAGE", image_data)
                self.message_response()
                self.add_one_message("AT")
                self.message_response()
            elif self.commandname == "system":
                if len(self.message) == 0:
                    userconfig.delete(self.groupid, self.userid)
                    self.add_one_message("AT")
                    self.add_one_message("TEXT", "\n清除专业设置成功")
                    self.message_response()
                else:
                    userconfig.update(self.groupid, self.userid, self.message)
                    self.add_one_message("AT")
                    self.add_one_message("TEXT", "\n配置成功，当前您bot的专业设置为：")
                    self.add_one_message("TEXT", self.message)
                    self.message_response()
                chatcontext.clear(self.groupid, self.userid)

            else:
                if len(self.message) == 0:
                    return
                query = chatcontext.construct_query(self.groupid, self.userid,
                                                    self.message, self.time)
                info("Q: '%s'", query)
                # answer = LLMAPI.text_davinci_003(query)
                role, answer = LLMAPI.chat_completions(query)
                self.add_one_message("AT")
                self.add_one_message("TEXT", "\n")
                self.add_one_message("TEXT", answer)
                self.message_response()
                chatcontext.add_one_Q_A(self.groupid, self.userid,
                                        self.message, role, answer,
                                        self.time)
        except Exception as e:
            answer = "OOPS! An exception occurred: {}".format(e)
            self.add_one_message("AT")
            self.add_one_message("TEXT", "\n")
            self.add_one_message("TEXT", answer)
            self.message_response()


class RequestHandler(BaseHTTPRequestHandler):
    """"""
    def do_POST(self):
        """"""
        length = int(self.headers.get('Content-length', 0))
        data = self.rfile.read(length).decode('utf-8')
        params = parse_qs(data)
        debug("params: '%s'", params)
        echostr = params.get('echostr', [None])[0]
        signature = params.get('signature', [None])[0]
        timestamp = params.get('timestamp', [None])[0]
        rn = params.get('rn', [None])[0]
        # https://qy.baidu.com/doc/index.html#/inner_serverapi/robot?id=验证url的有效性
        if echostr:
            if check_signature(signature, rn, timestamp, Token):
                self.send_response(200)
                self.send_header('Content-Type', 'text/plain; charset=utf-8')
                self.end_headers()
                self.wfile.write(echostr.encode())
                info("The Infoflow robot has been successfully configured.")
            else:
                self.send_response(400)
                self.send_header('Content-Type', 'text/plain; charset=utf-8')
                self.end_headers()
                self.wfile.write('check signature fail'.encode())
                error("The configuration of Infoflow robot failed, "
                      "please check if the parameters '--token' are specified correctly.")
            return
        msg_base64 = data.encode()
        encrypter = AESCipher(base64_urlsafe_decode(AESKey))
        decrypted = encrypter.decrypt(msg_base64)
        response_data = decrypted.decode('utf-8')

        robot = InfoflowRobot()
        query = robot.message_receive(response_data)
        debug("Q: '%s'", query)
        if robot.commandname is None:
            robot.query_chatgpt_api()
        else:
            record_task(robot)


# reference: https://qy.baidu.com/doc/index.html#/inner_serverapi/appendix
def check_signature(signature: str, rn: str, timestamp: str, access_token: str):
    """"""
    string = rn + timestamp + access_token
    encoded_string = string.encode("utf-8")
    md5 = hashlib.md5(encoded_string)
    md5_hex = md5.hexdigest()
    return signature == md5_hex


if __name__ == '__main__':
    argp = optparse.OptionParser(usage="usage: %prog [options]")
    argp.add_option("--log", type="choice",
                    choices=['debug', 'info', 'warning', 'error', 'critical'],
                    default='info')
    argp.add_option('--port', type='int', nargs=1,
                    help='Specify a HTTP server port', default=8888)
    argp.add_option('--token', type='string', nargs=1,
                    help='Specify the token for the Infoflow robot')
    argp.add_option('--aeskey', type='string', nargs=1,
                    help='Specify the encodingAESKey for the Infoflow robot')
    argp.add_option('--webhook', type='string', nargs=1,
                    help='Specify the webhook for the Infoflow robot')
    argp.add_option('--openai-api-key-path', type='string', nargs=1,
                    help='Path to file containing OpenAI API Key, '
                         'https://platform.openai.com/account/api-keys')
    argp.add_option('--image-resolution', type="choice",
                    help='Specify the resolution of the generated image, '
                         'default is \'256x256\'', 
                    choices=['256x256', '512x512', '1024x1024'],
                    default='256x256')
    argp.add_option('--proxy', type='string', nargs=1,
                    help='Specify the proxy server address')

    global opts
    (opts, args) = argp.parse_args()
    if len(args) != 0:
        print("ERROR: Exactly zero positional argument expected.")
        argp.print_usage()
        sys.exit(1)
    
    logging.basicConfig(level=getattr(logging, opts.log.upper()),
                        format='%(asctime)s.%(msecs)03d %(levelname)-8s %(message)s',
                        datefmt='%Y-%m-%d,%H:%M:%S')

    info("ChatGPT (OpenAI) Access to Infoflow Rebot version {0}".format(VERSION))

    if opts.token is None:
        Token = "wxuiYXqGSIYz9"
    else:
        Token = opts.token

    if opts.aeskey is None:
        AESKey = "0eDnttzkTi20crTV1OjCkO"
    else:
        AESKey = opts.aeskey

    if opts.webhook is None:
        Webhook = "http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=d53eccce7aef05112ba046a8809d1acb9"
    else:
        Webhook = opts.webhook

    APIKeyList = ["24.933476c810cb95ce05f48751aebead47.2592000.**********.282335-26619551"]

    chatcontext = ChatContext()
    userconfig = UserConfig("chatgpt-bot-user.config")

    httpd = HTTPServer(('0.0.0.0', opts.port), RequestHandler)
    info("Start the HTTP server, port: %d", opts.port)
    httpd.serve_forever()
