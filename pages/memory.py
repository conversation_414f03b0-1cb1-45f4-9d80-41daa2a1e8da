"""
    __init__.py
"""
# Streamlit UI
import streamlit as st
import os
import re
import random

from core.memory import MemoryManager
from pages.components.memory import change_memory_path_dialog, add_memory_dialog, update_memory_dialog, \
display_memories, save_all_change_dialog
from pages.components.label import add_label_dialog, update_label_dialog, delete_label_dialog

# 设置页面为宽页模式
st.set_page_config(layout="wide")


def init_memory_management():
    """"""
    if 'memory_manager' not in st.session_state:
        # 初始化MemoryManager
        st.session_state.memory_manager = MemoryManager()
    if 'memories' not in st.session_state:
        st.session_state.memories = []
        
    if 'home_dir' not in st.session_state:
        # 设置系统 home 路径为根目录
        home_dir = os.path.expanduser("./data/memory")
        st.session_state.home_dir = home_dir
        st.session_state.memory_manager.load_memory(st.session_state.home_dir)
  

def suggest_labels_from_text(text):
    """根据文本内容生成建议标签
    
    Args:
        text: 输入文本
        
    Returns:
        list: 建议的标签列表
    """
    # 获取所有可用的标签
    available_labels = list(st.session_state.memory_manager.labels.keys())
    
    if not available_labels:
        return []
    
    # 简单的标签建议逻辑
    # 1. 从文本中提取关键词
    words = re.findall(r'\b\w+\b', text.lower())
    
    # 2. 根据关键词匹配标签
    suggested_labels = []
    for label in available_labels:
        label_lower = label.lower()
        # 如果标签是关键词的子串，或者关键词是标签的子串
        if any(label_lower in word or word in label_lower for word in words):
            suggested_labels.append(label)
    
    # 3. 如果没有匹配的标签，随机选择一些标签
    if not suggested_labels and available_labels:
        # 随机选择1-3个标签
        num_labels = min(random.randint(1, 3), len(available_labels))
        suggested_labels = random.sample(available_labels, num_labels)
    
    return suggested_labels


def main():
    """"""
    # 初始化记忆管理系统
    init_memory_management()
    memory_manager = st.session_state.memory_manager
    
    st.title("Memory Management")
    
    # 侧边栏搜索和操作按钮
    with st.sidebar:
        st.title("Memory Options:")
        
        st.write("---")
        st.markdown(f"Memory Loaded from:\n\n`{st.session_state.home_dir}`")
        if st.button("Change the memory path?"):
            change_memory_path_dialog()
        
        if st.button("Save change"):
            save_all_change_dialog()
        if st.button("Govern 🤖"):
            ''

        # 搜索部分
        st.write("---")
        search_type = st.selectbox("Search Type", ["label", "keyword"], key="search_type")
        k = st.number_input("Results Count", min_value=1, max_value=100, value=10)
        
        if search_type == "label":
            # 使用标签名称作为选项
            label_options = {label['name']: label['id'] for label in memory_manager.list_labels()}
            selected_labels = st.multiselect("Select Labels", options=list(label_options.keys()), key="search_labels")
            if st.button("Search", key="search_btn"):
                if selected_labels:  # 只在选择了标签时进行搜索
                    st.session_state.memories = memory_manager.search_memory(
                        query="",
                        label_ids=selected_labels,
                        k=k,
                        search_type="label"
                    )
                else:
                    # 如果没有选择标签，显示所有记忆
                    st.session_state.memories = memory_manager._memory_data[::-1][:k]
                
        else:  # keyword
            query = st.text_input("Search Query")
            
            if st.button("Search", key="search_btn"):
                if query:  # 只在有查询词时进行搜索
                    st.session_state.memories = memory_manager.search_memory(
                        query=query,
                        k=k,
                        search_type="keyword"
                    )
                else:
                    # 如果没有查询词，显示所有记忆
                    st.session_state.memories = memory_manager._memory_data[::-1][:k]
        
        # 显示当前记忆数量
        st.markdown(f"- Total Memories: {len(memory_manager._memory_data)}\n"
                    f"- Displayed Memories: {len(st.session_state.memories)}")
        
        # 操作按钮
        st.write("---")
        if st.button("Add Memory", icon="📝"):
            add_memory_dialog()
        
        if st.button("New Label", icon="🏷"):
            add_label_dialog()
        if st.button("Edit Label", icon="✏️"):
            update_label_dialog()
        if st.button("Delete Label", icon="🗑️"):
            delete_label_dialog()

            
    
    # 主要内容区域
    with st.container(border=True):
        display_memories(st.session_state.memories, memory_manager)


if __name__ == "__main__":
    main()