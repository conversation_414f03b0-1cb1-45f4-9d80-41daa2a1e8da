"""
    label.py
"""
from core.memory import MemoryManager
import traceback
import time
# Streamlit UI
import streamlit as st


@st.dialog("Add your Label")
def add_label_dialog():
    """"""
    # 基本信息
    memory_label = st.text_input("Label", key="memory_label")
    label_description = st.text_area("Description", key="label_description")
    
    # 类别选择
    categories = ["concept", "topic", "task", "project", "other"]
    label_category = st.selectbox(
        "Category",
        options=categories,
        key="label_category"
    )
    
    # 同义词输入
    synonyms = st.text_input(
        "Synonyms (comma separated)",
        key="label_synonyms",
        help="Enter synonyms separated by commas"
    )
    
    # 父标签选择
    labels = st.session_state.memory_manager.list_labels()
    parent_options = {label['name']: label['id'] for label in labels}
    parent_label = st.selectbox(
        "Parent Label",
        options=["None"] + list(parent_options.keys()),
        key="parent_label"
    )
    
    submit_button = st.button(label='Submit Label', key="submit_label")
    if submit_button:
        if not memory_label or memory_label in parent_options:
            st.error("Label name cannot be empty or already exists")
            return
            
        try:
            # 处理同义词
            synonym_list = [s.strip() for s in synonyms.split(",")] if synonyms else []
            synonym_list = [s for s in synonym_list if s]  # 移除空字符串
            
            # 处理父标签
            parent_id = None if parent_label == "None" else parent_options[parent_label]
            
            st.session_state.memory_manager.add_label(
                label=memory_label,
                description=label_description,
                category=label_category,
                synonyms=", ".join(synonym_list),
                parent_id=parent_id
            )
            st.success("Label added successfully!")
            time.sleep(1)
            st.rerun()
        except Exception as e:
            st.error(f"Failed to add label: {str(e)}, traceback: {traceback.format_exc()}")


@st.dialog("Update Label")
def update_label_dialog():
    """"""
    labels = {label['name']: label for label in st.session_state.memory_manager.list_labels()}
    if not labels:
        st.warning("No labels available to update")
        return
        
    old_label_name = st.selectbox("Select Label to Update", list(labels.keys()), key="old_label")
    old_label = labels[old_label_name]
    
    # 基本信息更新
    new_label = st.text_input("New Label Name", value=old_label['name'], key="new_label")
    new_description = st.text_area("New Description", value=old_label['description'], key="new_description")
    
    # 类别更新
    categories = ["concept", "topic", "task", "project", "other"]
    new_category = st.selectbox(
        "New Category",
        options=categories,
        index=categories.index(old_label['category']) if old_label['category'] in categories else 0,
        key="new_category"
    )
    
    # 同义词更新
    current_synonyms = old_label['synonyms'] if hasattr(old_label, 'synonyms') else ""
    new_synonyms = st.text_input(
        "New Synonyms (comma separated)",
        value=current_synonyms,
        key="new_synonyms",
        help="Enter synonyms separated by commas"
    )
    
    # 父标签更新
    all_labels = st.session_state.memory_manager.list_labels()
    parent_options = {label['name']: label['id'] for label in all_labels
                     if label['id'] != old_label['id']}  # 排除自己
    current_parent_label = next(
        (label['name'] for label in all_labels.values() 
         if label['id'] == old_label['parent_id']),
        "None"
    ) if hasattr(old_label, 'parent_id') else "None"
    
    new_parent = st.selectbox(
        "New Parent Label",
        options=["None"] + list(parent_options.keys()),
        index=["None"] + list(parent_options.keys()).index(current_parent_label) 
        if current_parent_label in parent_options else 0,
        key="new_parent"
    )
    
    # 显示子标签（只读）
    if hasattr(old_label, 'children_ids') and old_label['children_ids']:
        children_labels = [all_labels[cid]['name'] for cid in old_label['children_ids'] 
                         if cid in all_labels]
        if children_labels:
            st.write("Child Labels (read-only):")
            st.write(", ".join(children_labels))
    
    submit_button = st.button(label='Update Label', key="update_label")
    
    if submit_button:
        if not new_label:
            st.error("New label name cannot be empty")
            return
            
        try:
            # 处理同义词
            synonym_list = [s.strip() for s in new_synonyms.split(",")] if new_synonyms else []
            synonym_list = [s for s in synonym_list if s]  # 移除空字符串
            
            # 处理父标签
            new_parent_id = None if new_parent == "None" else parent_options[new_parent]
            
            st.session_state.memory_manager.update_label(
                old_label=old_label_name,
                new_label=new_label,
                new_description=new_description,
                new_category=new_category,
                new_synonyms=", ".join(synonym_list),
                new_parent_id=new_parent_id
            )
            st.success("Label updated successfully!")
            time.sleep(1)
            st.rerun()
        except Exception as e:
            st.error(f"Failed to update label: {str(e)}, traceback: {traceback.format_exc()}")


@st.dialog("Delete Label")
def delete_label_dialog():
    """"""
    labels = {label['name']: label['id'] for label in st.session_state.memory_manager.list_labels()}
    label_name = st.selectbox("Select Label to Delete", list(labels.keys()), key="delete_label")
    submit_button = st.button(label='Delete Label', key="confirm_delete_label")
    if submit_button and label_name:
        result = st.session_state.memory_manager.delete_label(label_name)
        if result['success']:
            st.success(result['message'])
        else:
            st.warning(result['message'])
        time.sleep(1)
        st.rerun()

