<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="HttpUrlsUsage" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredUrls">
        <list>
          <option value="http://0.0.0.0" />
          <option value="http://127.0.0.1" />
          <option value="http://activemq.apache.org/schema/" />
          <option value="http://cxf.apache.org/schemas/" />
          <option value="http://java.sun.com/" />
          <option value="http://javafx.com/fxml" />
          <option value="http://javafx.com/javafx/" />
          <option value="http://json-schema.org/draft" />
          <option value="http://localhost" />
          <option value="http://maven.apache.org/POM/" />
          <option value="http://maven.apache.org/xsd/" />
          <option value="http://primefaces.org/ui" />
          <option value="http://schema.cloudfoundry.org/spring/" />
          <option value="http://schemas.xmlsoap.org/" />
          <option value="http://tiles.apache.org/" />
          <option value="http://www.ibm.com/webservices/xsd" />
          <option value="http://www.jboss.com/xml/ns/" />
          <option value="http://www.jboss.org/j2ee/schema/" />
          <option value="http://www.springframework.org/schema/" />
          <option value="http://www.springframework.org/security/tags" />
          <option value="http://www.springframework.org/tags" />
          <option value="http://www.thymeleaf.org" />
          <option value="http://www.w3.org/" />
          <option value="http://xmlns.jcp.org/" />
          <option value="http://{DEV_SERVER_IP_PORT}" />
          <option value="http://{QA_SERVER_IP_PORT}" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="JsonStandardCompliance" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="6">
            <item index="0" class="java.lang.String" itemvalue="scikit-image" />
            <item index="1" class="java.lang.String" itemvalue="mysqlclient" />
            <item index="2" class="java.lang.String" itemvalue="pandas" />
            <item index="3" class="java.lang.String" itemvalue="tensorflow" />
            <item index="4" class="java.lang.String" itemvalue="lxml" />
            <item index="5" class="java.lang.String" itemvalue="pycocotools" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N806" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>