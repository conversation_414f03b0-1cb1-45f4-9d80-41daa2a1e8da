import pytest
import os
import shutil
from datetime import datetime
from core.memory import MemoryManager, Memory, Label


class TestMemoryManager:
    @pytest.fixture(autouse=True)
    def setup_and_teardown(self):
        """在每个测试前设置测试环境，测试后清理"""
        # 设置测试数据库路径
        self.test_data_dir = "./data/test_memory"
        
        # 清理已存在的测试数据
        if os.path.exists(self.test_data_dir):
            shutil.rmtree(self.test_data_dir)
        
        # 创建测试目录
        os.makedirs(self.test_data_dir, exist_ok=True)
        
        # 设置测试数据库路径
        os.environ['MEMORY_DB_PATH'] = os.path.join(self.test_data_dir, "test_memory.db")
        
        # 创建 MemoryManager 实例
        self.manager = MemoryManager()
        
        yield  # 运行测试
        
        # 清理测试数据
        if os.path.exists(self.test_data_dir):
            shutil.rmtree(self.test_data_dir)

    def test_add_and_get_memory(self):
        """测试添加和获取记忆"""
        # 添加一个记忆
        memory_text = "这是一个测试记忆"
        summary = "测试记忆摘要"
        self.manager.add_memory(memory_text, summary)
        
        # 使用关键词搜索验证记忆是否添加成功
        results = self.manager.search_memory("测试", search_type="keyword")
        assert len(results) > 0
        assert results[0].original_text == memory_text
        assert results[0].summary == summary

    def test_add_and_get_label(self):
        """测试添加和获取标签"""
        # 添加父标签
        parent_label = "情感"
        parent_desc = "情感相关的标签"
        parent_category = "emotion"
        self.manager.add_label(parent_label, parent_desc, parent_category)
        
        # 添加子标签
        child_label = "快乐"
        child_desc = "快乐的情感"
        child_category = "emotion"
        parent_id = 1  # 假设是第一个标签
        self.manager.add_label(child_label, child_desc, child_category, parent_id)
        
        # 验证标签是否正确添加
        assert len(self.manager.labels) == 2
        assert 1 in self.manager.labels
        assert 2 in self.manager.labels
        assert self.manager.labels[2].parent_id == 1

    def test_memory_with_labels(self):
        """测试带标签的记忆"""
        # 添加标签
        self.manager.add_label("工作", "工作相关", "category")
        label_id = 1  # 假设是第一个标签
        
        # 添加带标签的记忆
        memory_text = "完成了项目开发"
        summary = "项目开发完成"
        self.manager.add_memory(memory_text, summary, label_ids=[label_id])
        
        # 通过标签搜索记忆
        results = self.manager.search_memory("", label_ids=[label_id], search_type="label")
        assert len(results) > 0
        assert results[0].original_text == memory_text
        assert label_id in results[0].label_ids

    def test_update_memory(self):
        """测试更新记忆"""
        # 添加记忆
        memory_text = "原始记忆"
        summary = "原始摘要"
        self.manager.add_memory(memory_text, summary)
        
        # 获取记忆ID
        memory_id = self.manager.memory_data[0].id
        
        # 更新记忆
        new_text = "更新后的记忆"
        new_summary = "更新后的摘要"
        self.manager.update_memory(memory_id, new_text, new_summary)
        
        # 验证更新
        updated_memory = next(mem for mem in self.manager.memory_data if mem.id == memory_id)
        assert updated_memory.original_text == new_text
        assert updated_memory.summary == new_summary

    def test_delete_memory(self):
        """测试删除记忆"""
        # 添加记忆
        memory_text = "要删除的记忆"
        summary = "要删除的摘要"
        self.manager.add_memory(memory_text, summary)
        
        # 获取记忆ID
        memory_id = self.manager.memory_data[0].id
        
        # 删除记忆
        self.manager.delete_memory(memory_id)
        
        # 验证删除
        assert len([mem for mem in self.manager.memory_data if mem.id == memory_id]) == 0

    def test_add_and_remove_synonyms(self):
        """测试添加和删除同义词"""
        # 添加记忆
        memory_text = "测试同义词"
        summary = "同义词测试"
        self.manager.add_memory(memory_text, summary)
        
        memory_id = self.manager.memory_data[0].id
        
        # 添加同义词
        synonyms = ["test synonyms", "测试"]
        self.manager.add_synonyms(memory_id, synonyms)
        
        # 验证同义词添加
        memory = next(mem for mem in self.manager.memory_data if mem.id == memory_id)
        assert all(syn in memory.synonyms for syn in synonyms)
        
        # 删除同义词
        self.manager.remove_synonyms(memory_id, ["test synonyms"])
        
        # 验证同义词删除
        memory = next(mem for mem in self.manager.memory_data if mem.id == memory_id)
        assert "test synonyms" not in memory.synonyms
        assert "测试" in memory.synonyms

    def test_search_with_exact_match(self):
        """测试精确匹配搜索"""
        # 添加两个相似的记忆
        self.manager.add_memory("Python编程", "Python学习笔记")
        self.manager.add_memory("Python3编程", "Python3学习笔记")
        
        # 精确匹配搜索
        results = self.manager.search_memory("Python编程", search_type="keyword", exact_match=True)
        assert len(results) == 1
        assert results[0].original_text == "Python编程"

    def test_trigger_operations(self):
        """测试触发器操作"""
        # 添加触发器
        trigger = "daily_review"
        description = "每日回顾"
        self.manager.add_trigger(trigger, description)
        
        # 添加带触发器的记忆
        memory_text = "今日总结"
        summary = "工作总结"
        self.manager.add_memory(memory_text, summary, trigger=trigger)
        
        # 通过触发器搜索
        results = self.manager.search_memory(trigger, search_type="trigger")
        assert len(results) > 0
        assert results[0].trigger == trigger
        
        # 更新触发器
        new_trigger = "weekly_review"
        self.manager.update_trigger(trigger, new_trigger, "每周回顾")
        
        # 验证触发器更新
        results = self.manager.search_memory(new_trigger, search_type="trigger")
        assert len(results) > 0
        assert results[0].trigger == new_trigger


if __name__ == "__main__":
    pytest.main(["-v", "test_memory_manager.py"]) 