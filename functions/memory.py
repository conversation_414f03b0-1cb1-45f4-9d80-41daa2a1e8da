"""
    __init__.py
"""
import streamlit as st
from typing import Optional


def list_labels() -> str:
    """
    列举标签信息，并以 Markdown 表格的形式返回，表格包含以下列：
      - 标签名称
      - 描述
      - 关联关系（父标签和子标签）
      - 同义词
      - 领域（category）
    """
    header = "| 标签名称 | 描述 | 关联关系 | 同义词 | 领域 |\n"
    header += "| --- | --- | --- | --- | --- |\n"
    rows = ""
    memory_manager = st.session_state.get('memory_manager')
    for label_id, lab in memory_manager.labels.items():
        # 构造关联关系：包含父标签信息，及若存在 children_ids 则显示子标签列表
        parent = lab.parent_id if lab.parent_id is not None else ""
        children = getattr(lab, "children_ids", [])
        relation = f"父: {parent}" if parent else ""
        if children:
            relation += f", 子: {', '.join(map(str, children))}"
        # 同义词可能存储为列表或字符串
        synonyms = ", ".join(lab.synonyms) if isinstance(lab.synonyms, list) else lab.synonyms
        row = f"| {lab.label} | {lab.description} | {relation} | {synonyms} | {lab.category} |\n"
        rows += row
    return header + rows


def list_memories() -> str:
    """
    列举记忆信息，并以 Markdown 表格的形式返回，表格包含以下列：
      - 标签（将 label_ids 转换为对应的标签名称列表）
      - 记忆摘要 (summary)
    """
    header = "| 标签 | 记忆摘要 |\n"
    header += "| --- | --- |\n"
    rows = ""
    memory_manager = st.session_state.get('memory_manager')
    for mem in memory_manager.memory_data:
        # 将 label_ids 转换为标签名称
        label_names = []
        for lid in mem.label_ids:
            if lid in memory_manager.labels:
                label_names.append(memory_manager.labels[lid].label)
        labels_str = ", ".join(label_names)
        # 构造表格行
        row = f"| {labels_str} | {mem.summary} |\n"
        rows += row
    return header + rows


def get_specific_memory(label: Optional[str] = None, keyword: Optional[str] = None, k: int = 5) -> str:
    """
    根据条件获取具体记忆，并返回一个字符串，格式为：
      xx时间创建，xx时间修改，内容为：xx
      xx时间创建，xx时间修改，内容为：xx

    参数优先级：先 label，最后 keyword
    """
    results = []
    memory_manager = st.session_state.get('memory_manager')

    if label is not None:
        label_ids = []
        for lid, lab in memory_manager.labels.items():
            if lab.label.lower() == label.lower() or (
                isinstance(lab.synonyms, list) and label.lower() in [s.lower() for s in lab.synonyms]
            ) or (
                isinstance(lab.synonyms, str) and label.lower() in lab.synonyms.lower()
            ):
                label_ids.append(lid)
        if label_ids:
            results = memory_manager.search_memory(query="", label_ids=label_ids, k=k, search_type="label")

    # 3. 若提供 keyword，则直接进行关键字搜索
    elif keyword is not None:
        results = memory_manager.search_memory(query=keyword, k=k, search_type="keyword")

    # 如果没有匹配的搜索条件，则返回空字符串
    if not results:
        return ""

    # 格式化输出结果，每条记忆一行
    output_lines = []
    for mem in results:
        # 假定 created_at 和 updated_at 均为 datetime 类型，可直接格式化输出
        line = f"{mem.created_at}创建，{mem.updated_at}修改，内容为：{mem.original_text}"
        output_lines.append(line)
    return "\n".join(output_lines)