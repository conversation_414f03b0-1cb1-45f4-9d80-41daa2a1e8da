#!/usr/bin/env python3
"""
MCP功能测试脚本 - 简化版
"""
import sys
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
    ]
)

try:
    from functions.mcp_config_manager import mcp_config_manager
    print('✅ 配置管理器导入成功')
    
    # 启用MCP
    mcp_config_manager.toggle_mcp_global(True)
    print(f'✅ MCP启用状态: {mcp_config_manager.mcp_enabled}')
    
    # 启用files服务
    mcp_config_manager.toggle_server_status('files', True)
    print(f'✅ files服务启用状态: {mcp_config_manager.is_server_enabled("files")}')
    
    # 导入工具模块
    from functions.mcp_tools import use_mcp_tool, check_mcp_status, get_all_mcp_tools_info
    print('✅ 工具模块导入成功')
    
    # 检查MCP状态
    status = check_mcp_status()
    print(f'✅ MCP状态: {status}')
    
    # 获取工具信息
    tools_info = get_all_mcp_tools_info()
    print(f'✅ 工具信息: {tools_info[:100]}...')
    
    # 尝试调用工具
    success, result = use_mcp_tool('files', 'list_directory', {'path': '/app'})
    print(f'✅ 工具调用结果: {"成功" if success else "失败"}, {result[:100] if success else result}...')
    
    print('🎉 MCP功能测试通过')
    
except Exception as e:
    print(f'❌ 测试失败: {e}')
    import traceback
    traceback.print_exc()
    sys.exit(1)
